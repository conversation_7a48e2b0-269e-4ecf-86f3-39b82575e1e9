import ProgressBarTabs from "@/components/ui/progress-bar/ProgressBar";
import React, { useEffect, useRef, useState } from "react";
import {
  ChevronDown,
  Loader2,
  ChevronLeft,
  ChevronRight,
  CircleX,
} from "lucide-react";
import {
  addAgentXToProject,
  DatasetRecommendationsResponse,
  Entity,
  getDatasetRecommendations,
} from "@/service/agentXService";
import FileUploader from "@/components/common/fileUploader/FileUploader";
import { Toast } from "@/components/ui/toast/toast";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { setActiveAssetTab } from "@/lib/store/features/project/projectAssetsSlice";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import AddAgentX from "@/components/common/addAgentx/AddAgentX";
import Tooltip from "@/components/ui/tooltip/Tooltip";

interface AddToProject {
  projectId: string;
  title: string;
  description: string;
  type?: string;
  url?: string;
  source?: string;
  tags?: string[];
}

export type ToastState = {
  message: string;
  type: "success" | "error";
} | null;

export default function AgentX({ projectId }: { projectId: string }) {
  const dispatch = useDispatch();
  const router = useRouter();
  const tags = ["db_long_name", "table_name"];
  const [loading, setLoading] = useState(false);
  const [totalSteps, setTotalSteps] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isDatasetOpen, setIsDatasetOpen] = useState(false);
  const [isRefreshed, setIsRefreshed] = useState(false);
  const [isSelected, setIsSelected] = useState(false);
  const [uploadEntity, setUploadEntity] = useState("");
  const [toast, setToast] = useState<ToastState>(null);
  const [entities, setEntities] = useState<Entity[]>([]);
  const [adding, setAdding] = useState(false);
  const [addingIndex, setAddingIndex] = useState<number | null>(null);
  const [recommendations, setRecommendations] =
    useState<DatasetRecommendationsResponse>();

  useEffect(() => {
    setIsOpen(true);
  }, [currentStep]);

  const getRecommendations = async (isRefreshed: boolean) => {
    try {
      setLoading(true);
      const data = await getDatasetRecommendations({
        project_id: projectId,
        refresh: isRefreshed,
      });
      if (data) {
        setRecommendations(data);
        setEntities(
          Array.isArray(data?.data?.matched_dataset_with_entity?.entities)
            ? data?.data?.matched_dataset_with_entity?.entities
            : [data?.data?.matched_dataset_with_entity?.entities]
        );
      }
      setLoading(false);
    } catch (error) {
      // console.log(error);
      setLoading(false);
    }
  };

  //const isFirstRender = useRef(true);

  useEffect(() => {
    // if (isFirstRender.current) {
    //   isFirstRender.current = false;
    //   return;
    // }
    getRecommendations(false);
  }, []);

  function formatString(input: string): string {
    if (!input) return "";
    return input
      .split("_") // Split by underscore
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize each word
      .join(" "); // Join with space
  }

  return (
    <div>
      {loading ? (
        <div className="mt-5 p-5 border-[2px] border-teal-500 rounded-md">
          <h1 className="flex gap-2">
            <img src="/assets/icons/ai-avatar-icon.svg" alt="#" />
            <span className="font-bold text-xl">Data Recommendations</span>
          </h1>
          <div className="grid grid-cols-2 gap-4 mt-2">
            {Array.from({ length: 2 }).map((_, index) => (
              <div
                key={index}
                className="w-full h-16 bg-gray-200 rounded-lg animate-pulse p-3"
              >
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <>
          {entities.length > 0 ? (
            <div>
              <div className="mt-5 p-5 border-[2px] border-teal-500 rounded-md">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col w-1/2">
                    <h1 className="flex gap-2">
                      <img src="/assets/icons/ai-avatar-icon.svg" alt="#" />
                      <span className="font-bold text-xl">Data Recommendations</span>
                    </h1>
                    {isOpen}
                  </div>
                  <div className="w-1/2 flex gap-2 justify-end">
                    {/* {currentStep >= entities.length && (
                  <button
                    className="text-md text-teal-500"
                    onClick={() => getRecommendations(true)}
                  >
                    Refresh
                  </button>
                )} */}
                    <ProgressBarTabs
                      currentStep={currentStep}
                      setCurrentStep={setCurrentStep}
                      totalSteps={entities.length}
                      isOpen={isOpen}
                    />
                    <ChevronDown
                      className={`w-6 h-6 cursor-pointer transition-transform duration-300 ${isOpen ? "rotate-180" : "rotate-0"
                        }`}
                      onClick={() => setIsOpen(!isOpen)}
                    />
                  </div>
                </div>

                <div
                  className={`transition-all duration-500 overflow-hidden ${isOpen ? "opacity-100 mt-2" : "max-h-0 opacity-0"
                    }`}
                >
                  {currentStep < entities.length && (
                    <div>
                      <h1 className="text-lg font-semibold mt-3">
                        {entities &&
                          formatString(
                            entities[currentStep]?.entity?.toString()
                          )}
                      </h1>
                      <p className="line-clamp-1">
                        {entities && entities[currentStep]?.entity_description}
                      </p>
                      <div className="grid grid-cols-2 gap-4 mt-5">
                        {entities &&
                          entities[currentStep]?.matched_entity_attributes?.map(
                            (item, index) => (
                              <div
                                key={`title-${item?.attribute}-${currentStep + index
                                  }`}
                                className="border-2 px-4 py-2 rounded-md border-gray-300"
                              >
                                <div className="flex flex-col">
                                  <div className="flex justify-between items-center">
                                    <h1 className="text-lg line-clamp-1">
                                      {entities &&
                                        formatString(
                                          item?.attribute?.toString()
                                        )}
                                    </h1>
                                    <AddAgentX
                                      projectId={projectId}
                                      title={`${entities[
                                        currentStep
                                      ]?.entity?.toString()}${" > "}${item.attribute?.toString()}`}
                                      description={`${entities[currentStep]
                                          ?.entity_description
                                        }${" "}${item.entity_attribute_description
                                        }`}
                                      tags={[
                                        ...[
                                          formatString(
                                            entities[
                                              currentStep
                                            ]?.entity?.toString()
                                          ),
                                          item.type,
                                          [
                                            item?.database,
                                            item?.dataset,
                                            item?.matched_dataset_attribute,
                                          ]
                                            .filter(Boolean)
                                            .join(" > "),
                                        ].filter(
                                          (tag) =>
                                            tag !== null &&
                                            tag !== undefined &&
                                            tag !== ""
                                        ),
                                      ]}
                                      isAdd={item.dataset ? true : false}
                                      isAddedtoProject={item.isAdded}
                                      setToast={setToast}
                                      getRecommendations={getRecommendations}
                                    />
                                  </div>
                                  {item.dataset ? (
                                    <>
                                      <p className="text-sm text-gray-500 line-clamp-1">
                                        {entities &&
                                          item.entity_attribute_description}
                                      </p>
                                      <span className="flex flex-wrap gap-2 mt-1 mb-1">
                                        <div className="flex items-center gap-1">
                                          <span className="px-2 text-sm bg-gray-200 text-gray-700 rounded-md">
                                            {item.database
                                              ?.split("_")
                                              ?.join(" ")}
                                          </span>
                                          <span className="font-bold text-md">
                                            {item.dataset && (
                                              <ChevronRight className="w-5 h-5 font-semibold" />
                                            )}
                                          </span>
                                          <span className="px-2 text-sm bg-gray-200 text-gray-700 rounded-md">
                                            {item?.dataset}
                                          </span>
                                          <span className="font-bold text-md">
                                            {item.matched_dataset_attribute && (
                                              <ChevronRight className="w-5 h-5 font-semibold" />
                                            )}
                                          </span>
                                          <span className="px-2 text-sm bg-gray-200 text-gray-700 rounded-md">
                                            {item.matched_dataset_attribute}
                                          </span>
                                        </div>
                                      </span>
                                      <div className="flex flex-row gap-1 items-center mt-1">
                                        <button className="px-2 border-2 border-teal-500 text-teal-500 rounded-md text-sm">
                                          {item.confidence_score}% Confidence
                                        </button>
                                        <div className="flex items-center justify-center">
                                          <Tooltip text="Relevance score for your projects" />
                                        </div>
                                      </div>
                                    </>
                                  ) : (
                                    <span className="mt-1">
                                      <button className="px-2 py-1 text-sm bg-gray-300 text-gray-700 rounded-md">
                                        no data source found
                                      </button>
                                    </span>
                                  )}
                                </div>
                                {!item.dataset && (
                                  <p className="text-sm mt-1">
                                    {entities &&
                                      formatString(
                                        item.attribute?.toString()
                                      )}{" "}
                                    data will benefit this project but we
                                    coudn't find any source from your data. If
                                    you can{" "}
                                    <b>
                                      upload some{" "}
                                      {entities &&
                                        formatString(
                                          item.attribute?.toString()
                                        )}{" "}
                                      data assets.
                                    </b>
                                  </p>
                                )}
                              </div>
                            )
                          )}
                      </div>
                    </div>
                  )}
                  {currentStep >= entities.length && (
                    <div>
                      <h1 className="text-lg font-semibold mt-3">Summary</h1>
                      <div className="grid grid-cols-2 gap-4 mt-5">
                        {entities &&
                          entities?.map((item, index) => (
                            <div
                              key={`${item.entity}${index}`}
                              className="flex gap-5 items-center border-2 px-4 py-2 rounded-md border-gray-300"
                            >
                              <h1 className="text-lg line-clamp-1">
                                {formatString(item.entity?.toString())}
                              </h1>
                              <div>
                                <button className="px-2 py-1 text-sm bg-gray-300 text-gray-700 rounded-md">
                                  {
                                    item.matched_entity_attributes.filter(
                                      (attr) => attr.isAdded
                                    ).length
                                  }{" "}
                                  data source added
                                </button>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {isDialogOpen && (
                <FileUploader
                  setIsDialogOpen={setIsDialogOpen}
                  uploadEntity={uploadEntity}
                />
              )}
            </div>
          ) : (
            <div className="flex flex-row gap-52 items-center mt-5 p-5 border-[2px] border-teal-500 rounded-md">
              <h1 className="flex gap-2">
                <img src="/assets/icons/ai-avatar-icon.svg" alt="#" />
                <span className="font-bold text-xl">Data Recommendations</span>
              </h1>
              <div className="flex gap-1 justify-center text-sm">
                <CircleX className="w-5 h-5" />
                There are <b>No Recommendations</b> at this time
              </div>
            </div>
          )}
        </>
      )}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
}
