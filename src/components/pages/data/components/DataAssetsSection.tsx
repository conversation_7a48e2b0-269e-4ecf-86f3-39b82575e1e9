"use client"

import { Database } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import Tooltip from "@/components/ui/tooltip/Tooltip"
import DataProgressBar from "./DataProgressBar"
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip as RechartsTooltip } from 'recharts'
import { useEffect, useState } from "react"
import { DatasageService } from "@/service/datasage"
import { datasetService } from "@/service/datasetService"
import { ShimmerCard } from "./ActionSection"

type FileTypeItemProps = {
  color: string
  type: string
}

type FileTypeData = {
  count: number
  type: string
}

type PieChartData = {
  name: string
  value: number
  color: string
}

function FileTypeItem({ color, type }: FileTypeItemProps) {
  return (
    <div className="flex items-center gap-2">
      <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: color }}></div>
      <span className="text-sm text-[#333333]">{type}</span>
    </div>
  )
}

type PipelineItemProps = {
  label: string
  count: string
  color?: string
  tooltipText?: string
}

function PipelineItem({ label, count, color = "text-[#333333]", tooltipText }: PipelineItemProps) {
  return (
    <div className="flex justify-between items-center">
      <div className="flex items-center gap-2">
        <span className="text-sm text-[#666666]">{label}</span>
        {tooltipText && <Tooltip text={tooltipText} />}
      </div>
      <span className={`text-sm font-medium ${color}`}>{count} files</span>
    </div>
  )
}

// Function to generate colors for file types
const generateFileTypeColors = (fileTypes: FileTypeData[]): string[] => {
  const colors = [
    '#a855f7', // purple
    '#dc2625', // red
    '#00b2a1', // teal
    '#3c82f6', // blue
    '#eaa23b', // orange
    '#10b981', // green
    '#f59e0b', // amber
    '#8b5cf6', // violet
    '#ef4444', // red-500
    '#06b6d4', // cyan
  ];

  return fileTypes.map((_, index) => colors[index % colors.length]);
};

// Function to capitalize first letter of file type names
const capitalizeFileType = (type: string): string => {
  return type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();
};

// Custom tooltip component for pie chart
const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-900">{data.name}</p>
        <p className="text-sm text-gray-600">
          Count: <span className="font-semibold">{data.value}</span>
        </p>
      </div>
    );
  }
  return null;
};

export default function DataAssetsSection() {
  const [ processingData, setProcessingData ] = useState<({
    totalFiles: {
      count: number
      label: string
      tooltipText?: string
      color?: string
    },
    inProcessFiles: {
      count: number
      label: string
      tooltipText?: string
      color?: string
    },
    silverFiles: {
      count: number
      label: string
      tooltipText?: string
      color?: string
    },
    goldFiles: {
      count: number
      label: string
      tooltipText?: string
      color?: string
    },
  })>({
    totalFiles: {
      count: 0,
      label: "Total Data Ingested",
      tooltipText: "Connected and ingested into dataplace",
    },
    inProcessFiles: {
      count: 0,
      label: "In Process",
      tooltipText: "Transforming and generating contextual metadata",
      color: "text-[#3C82F6]",
    },
    silverFiles: {
      count: 0,
      label: "Silver Level",
      tooltipText: "Basic transformations complete",
      color: "text-[#A855F7]",
    },
    goldFiles: {
      count: 0,
      label: "Gold Level (AI Ready)",
      tooltipText: "Available for BI tools, ML models, and AI teams",
      color: "text-[#EBB305]",
    },
  });

  const [totalFiles, setTotalFiles] = useState(0);
  const [pieChartData, setPieChartData] = useState<PieChartData[]>([]);
  const [fileTypesTotalFiles, setFileTypesTotalFiles] = useState(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Fetch processing data
        const res = await DatasageService.getDatasageData();
        console.log("Fetched Processing Data:", res.data);
        setTotalFiles(res.data.summary.total_unstructured_files);
        const fetchedProcessingData = res.data.summary.asset_status;

        // Update processing data
        setProcessingData({
          totalFiles: {
            ...processingData.totalFiles,
            count: fetchedProcessingData.total,
          },
          inProcessFiles: {
            ...processingData.inProcessFiles,
            count: fetchedProcessingData.in_process,
          },
          silverFiles: {
            ...processingData.silverFiles,
            count: fetchedProcessingData.silver,
          },
          goldFiles: {
            ...processingData.goldFiles,
            count: fetchedProcessingData.gold,
          },
        });

        // Fetch file types statistics
        const fileTypesResData = await datasetService.getFileTypesStatistics();
        const fileTypesRes = fileTypesResData.data;
        console.log("Fetched File Types Data:", fileTypesRes.data);

        if (fileTypesRes.data && fileTypesRes.data.fileTypes) {
          const fileTypes = fileTypesRes.data.fileTypes;
          setFileTypesTotalFiles(fileTypesRes.data.totalFiles);

          // Generate colors and create pie chart data
          const colors = generateFileTypeColors(fileTypes);
          const chartData: PieChartData[] = fileTypes.map((fileType: FileTypeData, index: number) => ({
            name: capitalizeFileType(fileType.type),
            value: fileType.count,
            color: colors[index]
          }));
          console.log("pie chart data", chartData)
          setPieChartData(chartData);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  },[]);

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Data Assets
        </CardTitle>
      </CardHeader>
      {loading ? (
        <>
          <ShimmerCard />
        </>
      ) : (
        <CardContent>
        <div className="flex gap-6">
          <div className="border rounded-md border-[#e0e0e0] w-content">
            <div className="flex items-center">
              <div className="space-y-3 px-6">
                <div className="mb-4">
                  <div className="text-3xl pt-3 font-bold text-[#333333]">{fileTypesTotalFiles || totalFiles}</div>
                  <div className="text-[#666666] text-sm">Total files</div>
                </div>
                <div className="flex flex-wrap gap-4">
                  {pieChartData.map((item, index) => (
                  <FileTypeItem
                    key={index}
                    color={item.color}
                    type={item.name}
                  />
                ))}
                </div>
              </div>

              <div className="relative ml-12">
                <PieChart width={240} height={240}>
                  <Pie
                    data={pieChartData}
                    cx={120}
                    cy={120}
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                    animationBegin={0}
                    animationDuration={800}
                  >
                    {pieChartData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={entry.color}
                        style={{ cursor: 'pointer' }}
                      />
                    ))}
                  </Pie>
                  <RechartsTooltip content={<CustomTooltip />} />
                </PieChart>
              </div>
            </div>
          </div>

          <div className="border rounded-md border-[#e0e0e0] p-6 w-full">
            <div className="mb-4">
              <h4 className="font-bold text-[#333333] mb-2">Processing Pipeline</h4>
              <p className="text-[#666666] text-sm">Current status of data processing workflows</p>
            </div>

            <div className="space-y-3">
              {
                Object.entries(processingData).map(([key, value]) => (
                  <PipelineItem
                    key={key}
                    label={value.label}
                    count={value.count.toString()}
                    color={value.color}
                    tooltipText={value.tooltipText}
                  />
                ))}
            </div>

            <div className="mt-4">
              <DataProgressBar
                totalFiles={processingData.totalFiles.count}
                inProcessFiles={processingData.inProcessFiles.count}
                silverFiles={processingData.silverFiles.count}
                goldFiles={processingData.goldFiles.count}
              />
            </div>
          </div>
        </div>
      </CardContent>
        )}
    </Card>
  )
}
