"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { AlertTriangle, Plus, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, ArrowUpRight, Info, TriangleAlert } from "lucide-react"
import { Badge } from "@/components/ui/badge/badge"
import { Button } from "@/components/ui/button/button"
import { Progress } from "@/components/ui/progress/progress"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Toast } from "@/components/ui/toast/toast"
import { dataService } from "@/service/dataService"
import { ActionItemsResponse, ActionItemData } from "@/types/dataset"
import UploadModalProject from "./UploadModalProject";
import { ConnectionItemProps } from "./ConnectionsSection"

type ActionItemProps = {
  type: string
  title: string
  subtitle: string
  status: string
  statusColor: string
  progress?: number
  note?: string
  showActions?: boolean
  projectId?: string
}

function ActionItem({
  type,
  title,
  subtitle,
  status,
  statusColor,
  progress,
  note,
  showActions = false,
  projectId,
  onUploadData,
}: ActionItemProps & { onUploadData?: () => void }) {
  const router = useRouter();
  const typeColors = {
    Critical: "bg-[#fee2e1] text-[#dc2625]",
    High: "bg-[#ffedd5] text-[#9a3413]",
    Medium: "bg-[#fef9c3] text-[#854d0f]",
  }

  const handleGoToProject = () => {
    if (projectId) {
      router.push(`/projects/dashboard/${projectId}`);
    }
  };

  return (
    <div className="border border-[#e4e4e4] rounded-lg p-4 bg-white">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <svg width="20" height="20" viewBox="0 0 40.609 40.609" className="text-[#888faa]">
              <path d="M180.3-759.391q-8.9,0-14.6-2.267t-5.7-5.835v-24.893q0-3.163,5.938-5.388A41.051,41.051,0,0,1,180.3-800a41.051,41.051,0,0,1,14.367,2.226q5.938,2.225,5.938,5.388v24.893q0,3.568-5.7,5.835T180.3-759.391Zm0-28.2a41.374,41.374,0,0,0,10.992-1.521q5.54-1.52,6.628-3.312-1.039-1.889-6.524-3.465a39.971,39.971,0,0,0-11.1-1.577,41.8,41.8,0,0,0-11.082,1.52q-5.552,1.52-6.636,3.326,1.035,1.9,6.538,3.466A40.751,40.751,0,0,0,180.3-787.588Zm0,12.773a51.339,51.339,0,0,0,5.14-.254,42.986,42.986,0,0,0,4.825-.754,30.244,30.244,0,0,0,4.275-1.247,21.564,21.564,0,0,0,3.527-1.683v-10.4a21.6,21.6,0,0,1-3.527,1.684,30.2,30.2,0,0,1-4.275,1.247,42.915,42.915,0,0,1-4.825.754,51.339,51.339,0,0,1-5.14.254,48.778,48.778,0,0,1-5.276-.279,45.275,45.275,0,0,1-4.84-.779,31.424,31.424,0,0,1-4.22-1.223,18.589,18.589,0,0,1-3.432-1.659v10.4a18.577,18.577,0,0,0,3.432,1.659,31.541,31.541,0,0,0,4.22,1.223,45.475,45.475,0,0,0,4.84.779A48.921,48.921,0,0,0,180.3-774.815Zm0,12.886a49.553,49.553,0,0,0,6.2-.371,34.426,34.426,0,0,0,5.284-1.051,18.141,18.141,0,0,0,3.983-1.626,6.2,6.2,0,0,0,2.3-2.042v-9.2a21.564,21.564,0,0,1-3.527,1.683,30.244,30.244,0,0,1-4.275,1.247,42.987,42.987,0,0,1-4.825.754,51.339,51.339,0,0,1-5.14.254,48.921,48.921,0,0,1-5.276-.278,45.475,45.475,0,0,1-4.84-.779,31.541,31.541,0,0,1-4.22-1.223,18.577,18.577,0,0,1-3.432-1.659V-767a5.8,5.8,0,0,0,2.292,2.06,19.09,19.09,0,0,0,3.975,1.594A34.411,34.411,0,0,0,174.1-762.3,49.737,49.737,0,0,0,180.3-761.929Z" transform="translate(-160 800)" fill="currentColor" />
            </svg>
            <span className="text-sm font-medium text-[#333333]">Data Recommendation</span>
          </div>

          <Badge className={`${typeColors[type as keyof typeof typeColors]} flex items-center gap-1`}>
            <AlertTriangle className="w-3 h-3" />
            {type}
          </Badge>
        </div>
        {showActions && (
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-[#00b2a1] hover:text-[#018e42] h-8 px-3"
              onClick={handleGoToProject}
            >
              <ArrowUpRight className="w-4 h-4 mr-1" />
              Go to Project
            </Button>
            <Button size="sm" variant="outline" className="text-[#00b2a1] border-[#00b2a1] rounded-[4px] h-8 px-3 hover:bg-white hover:text-[#00b2a1]" onClick={onUploadData}>
              <Plus className="w-4 h-4 mr-1" />
              Upload Data
            </Button>
          </div>
        )}
      </div>

      <div className="mb-3">
        <p className="text-sm text-[#666666]">{subtitle}</p>
        <p className="text-base font-semibold text-[#333333]">{title}</p>
      </div>

      {note && (
        <div >
          <p className="text-sm font-medium text-[#333333] mb-2">{note}</p>
          {progress !== undefined && (
            <div>
              <Progress
                value={progress}
                className="h-2 mb-2 bg-[#f0f2f8]"
                indicatorClassName="bg-[#3C82F6]"
              />
              <div className="flex justify-between text-xs text-[#666666]">
                <span className={statusColor}>{status}</span>
                <span>{progress.toFixed(1)}%</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export function ShimmerCard() {
  return (
    <div className="border border-[#e4e4e4] rounded-lg p-4 bg-white animate-pulse">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
            <div className="w-32 h-4 bg-gray-200 rounded"></div>
          </div>
          <div className="w-16 h-5 bg-gray-200 rounded-full"></div>
        </div>
        <div className="flex gap-2">
          <div className="w-20 h-8 bg-gray-200 rounded"></div>
          <div className="w-24 h-8 bg-gray-200 rounded"></div>
        </div>
      </div>
      <div className="mb-3">
        <div className="w-48 h-3 bg-gray-200 rounded mb-1"></div>
        <div className="w-32 h-4 bg-gray-200 rounded"></div>
      </div>
      <div className="mb-3">
        <div className="w-32 h-3 bg-gray-200 rounded mb-2"></div>
        <div className="w-full h-2 bg-gray-200 rounded mb-2"></div>
        <div className="flex justify-between">
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
          <div className="w-8 h-3 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  )
}

export default function ActionSection({ inactiveDbms }: { inactiveDbms: ConnectionItemProps[] }) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [actionItems, setActionItems] = useState<ActionItemsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [limit] = useState(2);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [uploadCancel, setUploadCancel] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [selectedDescription, setSelectedDescription] = useState<string | null>(null);
  const [selectedActionItem, setSelectedActionItem] = useState<ActionItemData | null>(null);

  const [toast, setToast] = useState<{ message: string; type: "success" | "error" } | null>(null);
  const [previousUploadModalState, setPreviousUploadModalState] = useState(false);

  const fetchActionItems = async (page: number) => {
    try {
      setLoading(true);
      const response = await dataService.getActionItems(page, limit);
      console.log("Action Items API Response:", response);
      setActionItems(response);
    } catch (error) {
      console.error("Error fetching action items:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActionItems(currentPage);
  }, [currentPage]);

  // Detect when upload modal closes and show toast + refetch data
  useEffect(() => {
    if (previousUploadModalState && !uploadModalOpen && !uploadCancel) {
      // Modal was open and is now closed
      setToast({
        message: "Files uploaded successfully!",
        type: "success"
      });
      fetchActionItems(currentPage);
    }
    if (uploadCancel) setUploadCancel(!uploadCancel);
    setPreviousUploadModalState(uploadModalOpen);
  }, [uploadModalOpen, previousUploadModalState, uploadCancel, currentPage]);

  const handleNextPage = () => {
    if (actionItems && currentPage < Math.ceil(actionItems.total / limit)) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const totalPages = actionItems ? Math.ceil(actionItems.total / limit) : 0;

  return (
    <Card className="mb-6 bg-white">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Info className="w-5 h-5 text-[#EAA23B]" />
            <CardTitle className="text-lg">Action Needed</CardTitle>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="bg-[#fee2e1] text-[#dc2625]">
              {loading ? "Loading..." : `${actionItems?.total || 0} Items`}
            </Badge>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePrevPage}
                disabled={currentPage <= 1 || loading}
                className="p-1"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm text-[#666666] min-w-[60px] text-center">
                {currentPage} / {totalPages}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleNextPage}
                disabled={currentPage >= totalPages || loading}
                className="p-1"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="p-0 hover:bg-transparent"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronUp className="h-5 w-5 text-[#666666]" />
              ) : (
                <ChevronDown className="h-5 w-5 text-[#666666]" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent className="space-y-4">
          {inactiveDbms.map((conn, index) => (
            <div className="border border-[#e4e4e4] bg-white rounded-lg p-4">
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-bold text-[#333333]">{conn.name}</span>
                  <Badge variant={"outline"} className="bg-red-100 text-[#dc2625] rounded-[16px]"><TriangleAlert className="w-4 h-4" /> Critical</Badge>
                </div>
                <Badge variant="destructive" className={`relative top-5 right-2 text-sm bg-[#DCFCE7] !rounded-[16px] ${conn.statusColor}`}>• Inactive</Badge>
              </div>
              <p className="text-sm text-[#666666] mb-2">Last Sync: {conn.lastSync}</p>
              <p className="text-sm text-[#666666]">{conn.failureReason}</p>
            </div>
          ))}
          {loading ? (
            <>
              <ShimmerCard />
              <ShimmerCard />
            </>
          ) : actionItems && actionItems.data.length > 0 ? (
            actionItems.data.map((item: ActionItemData, index: number) => (
              <ActionItem
                key={`${item._id}-${item.entity}-${index}`}
                type={item.matched_entity_attributes.length > 0 ? "High" : "Medium"}
                title={item.entity}
                subtitle={`Project: ${item.name}`}
                status={`${item.matched_entity_attributes.length} attributes need data`}
                statusColor={item.matched_entity_attributes.length > 0 ? "text-[#eaa23b]" : "text-[#666666]"}
                progress={item.dataCompletionPercentage}
                note="Data Completion"
                showActions={true}
                projectId={item._id}
                onUploadData={() => {
                  setSelectedProjectId(item._id);
                  setUploadModalOpen(true);
                  setSelectedDescription(item.entity_description);
                  setSelectedActionItem(item);
                }}
              />
            ))
          ) : (
            <div>
              <p className="text-sm text-[#666666]">No action items found</p>
            </div>
          )}
        </CardContent>)}
      <UploadModalProject
        open={uploadModalOpen}
        setOpen={setUploadModalOpen}
        projectId={selectedProjectId}
        description={selectedDescription || "Manually Uploaded"}
        actionItem={selectedActionItem}
        onCancel={() => setUploadCancel(true)}
      />
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
          dismissTime={5000}
        />
      )}
    </Card>
  )
}

