"use client";

import type React from "react";

import { useState, useRef, useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Upload, FileText, X, Loader2, Check } from "lucide-react";
import { getProjects } from "@/service/projects";
import filesService from "@/service/files";
import { addAgentXToProject } from "@/service/agentXService";
import { ActionItemData } from "@/types/dataset";

interface UploadedFile {
  name: string;
  size: number;
  file: File;
}

interface UploadStatus {
  uploading: boolean;
  success: boolean;
  error: boolean;
}

interface UploadModalProjectProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  projectId?: string | null;
  description: string;
  actionItem?: ActionItemData | null;
  onCancel: () => void;
}

export default function UploadModalProject({ open, setOpen, projectId, description, actionItem, onCancel }: UploadModalProjectProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [projects, setProjects] = useState<{ id: string; name: string }[]>([]);
  const [fetchedProjects, setFetchedProjects] = useState<{ id: string; name: string }[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatuses, setUploadStatuses] = useState<UploadStatus[]>([]);

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      const response = await getProjects({ limit: 100 });
      const projectNamesAndIds = response.projects.map((project) => ({
        id: project._id,
        name: project.name,
      }));
      console.log("Fetched Projects:", projectNamesAndIds);
      setFetchedProjects(projectNamesAndIds);
    };

    fetchProjects();
  }, []);

  // Pre-select projectId for all files if provided and files are added
  useEffect(() => {
    if (projectId && uploadedFiles.length > 0) {
      setProjects((prev) =>
        uploadedFiles.map((_, idx) => {
          // Only set if not already set
          if (prev[idx] && prev[idx].id) return prev[idx];
          const found = fetchedProjects.find((p) => p.id === projectId);
          return found ? { id: found.id, name: found.name } : { id: projectId, name: "" };
        })
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId, uploadedFiles.length, fetchedProjects]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return (
      Number.parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i]
    );
  };

  const handleFiles = (files: FileList) => {
    const newFiles: UploadedFile[] = [];
    Array.from(files).forEach((file) => {
      if (
        file.type.includes("spreadsheet") ||
        file.name.endsWith(".csv") ||
        file.name.endsWith(".xlsx") ||
        file.name.endsWith(".xls")
      ) {
        newFiles.push({
          name: file.name,
          size: file.size,
          file: file,
        });
      }
    });
    setUploadedFiles((prev) => [...prev, ...newFiles]);
    setProjects((prev) => [...prev, ...newFiles.map(() => ({ name: "", id: "" }))]);
    setUploadStatuses((prev) => [...prev, ...newFiles.map(() => ({ uploading: false, success: false, error: false }))]);
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    handleFiles(files);
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
    setProjects((prev) => prev.filter((_, i) => i !== index));
    setUploadStatuses((prev) => prev.filter((_, i) => i !== index));
  };

  const updateProject = (index: number, project: { id: string; name: string }) => {
    setProjects((prev) => prev.map((p, i) => (i === index ? project : p)));
  };



  const handleUpload = async () => {
    setIsUploading(true);
    try {
      for (let i = 0; i < uploadedFiles.length; i++) {
        const file = uploadedFiles[i];
        const fileExtension = file.name.split(".").pop()?.toLowerCase() || "";
        const projectId = projects[i].id;
        // Automatically use the first attribute if actionItem exists
        const selectedAttribute = actionItem && actionItem.matched_entity_attributes.length > 0
          ? actionItem.matched_entity_attributes[0].attribute
          : "";

        if (file && projectId) {
          // Set uploading status for this file
          setUploadStatuses((prev) =>
            prev.map((status, idx) =>
              idx === i ? { ...status, uploading: true, success: false, error: false } : status
            )
          );

          try {
            const response = await filesService.uploadFile(file.file, file.name, projectId);
            if (response.status !== 201) {
              throw new Error("Upload failed");
            }

            // Create the title based on entity and attribute if available
            const title = actionItem && selectedAttribute
              ? `${actionItem.entity} > ${selectedAttribute}`
              : file.name || "";

            await addAgentXToProject(
              {
                projectId: projectId,
                title: title,
                description: description,
                type: fileExtension,
                url: response.data.url,
                source: "Manual Upload",
                tags: [fileExtension],
              }
            );

            // Set success status for this file
            setUploadStatuses((prev) =>
              prev.map((status, idx) =>
                idx === i ? { ...status, uploading: false, success: true, error: false } : status
              )
            );
          } catch (error) {
            console.error(`Upload failed for ${file.name}:`, error);
            // Set error status for this file
            setUploadStatuses((prev) =>
              prev.map((status, idx) =>
                idx === i ? { ...status, uploading: false, success: false, error: true } : status
              )
            );
          }
        }
      }

      // Wait a bit to show success states before clearing
      setTimeout(() => {
        setUploadedFiles([]);
        setProjects([]);
        setUploadStatuses([]);
      }, 2000);
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      setIsUploading(false);
      setOpen(false);
    }
  };

  const handleCancel = () => {
    setUploadedFiles([]);
    setProjects([]);
    setOpen(false);
    onCancel();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Upload Data Files
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDragOver
              ? "border-teal-400 bg-teal-50"
              : "border-gray-300 bg-gray-50"
              }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-600 mb-4">Drag and drop files here, or</p>
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-2"
            >
              Browse Files
            </Button>
            <p className="text-sm text-gray-500 mt-3">
              Supported CSV, XLSX, XLS files
            </p>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".csv,.xlsx,.xls"
              onChange={handleFileInput}
              className="hidden"
            />
          </div>

          {/* Selected Files and Project Selection */}
          {uploadedFiles.length > 0 && (
            <div className="flex w-full">
              {/* Selected Files */}
              <div className="w-full">
                <div className="flex justify-between mb-4">
                  <h3 className="font-medium text-gray-900 mb-3">
                    Selected Files
                  </h3>
                  <h3 className="font-medium text-gray-900 mb-3 pr-[75px]">
                    {actionItem ? "File Destination" : "Choose Project"}
                  </h3>
                </div>
                <div className="space-y-3 w-full">
                  {uploadedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-3 p-2 bg-[#f9fafb] rounded-lg shadow-sm"
                    >
                      <FileText className="h-5 w-5 text-gray-400" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {file.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {actionItem ? (
                          // Show File Destination with attribute dropdown
                          <div className="flex-1">
                            <div className="text-sm text-gray-600 mb-1">
                              {actionItem.name} &gt; {actionItem.entity}
                            </div>
                          </div>
                        ) : (
                          // Show project dropdown for non-action item uploads (name)
                          <Select
                            value={projects[index].id}
                            onValueChange={(value) => updateProject(index, { id: value, name: fetchedProjects.find((p) => p.id === value)?.name || "" })}
                            disabled={uploadStatuses[index]?.uploading}
                          >
                            <SelectTrigger className="flex-1">
                              <SelectValue placeholder="Select a Project" />
                            </SelectTrigger>
                            <SelectContent>
                              {fetchedProjects.map((project) => (
                                <SelectItem key={project.id} value={project.id}>
                                  {project.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </div>

                      {/* Upload Status Indicator */}
                      {uploadStatuses[index]?.uploading && (
                        <div className="flex items-center justify-center w-6 h-6">
                          <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
                        </div>
                      )}

                      {uploadStatuses[index]?.success && (
                        <div className="flex items-center justify-center w-6 h-6">
                          <Check className="h-4 w-4 text-green-500" />
                        </div>
                      )}

                      {uploadStatuses[index]?.error && (
                        <div className="flex items-center justify-center w-6 h-6">
                          <X className="h-4 w-4 text-red-500" />
                        </div>
                      )}

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          removeFile(index);
                          updateProject(index, { id: "", name: "" });
                        }}
                        className="h-6 w-6 p-0 hover:bg-gray-100"
                        disabled={uploadStatuses[index]?.uploading}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Footer Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="ghost"
              onClick={handleCancel}
              className="text-gray-600 hover:text-gray-800"
            >
              Cancel
            </Button>
            {isUploading ? (
              <Button
                disabled
                className="bg-gray-400 hover:bg-gray-500 text-white"
              >
                Uploading...
              </Button>
            ) : (
              <Button
                onClick={handleUpload}
                disabled={
                  uploadedFiles.length === 0 ||
                  (!actionItem && projects.some(p => !p.id))
                }
                className="bg-teal-600 hover:bg-teal-700 text-white px-6"
              >
                Upload {uploadedFiles.length} File
                {uploadedFiles.length !== 1 ? "s" : ""}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
