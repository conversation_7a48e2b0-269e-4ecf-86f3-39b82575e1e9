"use client"

import React from "react"

interface DataProgressBarProps {
  totalFiles: number
  inProcessFiles: number
  silverFiles: number
  goldFiles: number
  className?: string
}

export default function DataProgressBar({
  totalFiles,
  inProcessFiles,
  silverFiles,
  goldFiles,
  className = ""
}: DataProgressBarProps) {
  // For better visibility, let's use fixed proportions: 50%, 20%, 30%
  const blueSegmentWidth = inProcessFiles / totalFiles * 100
  const graySegmentWidth = silverFiles / totalFiles * 100
  const purpleSegmentWidth = goldFiles / totalFiles * 100

  return (
    <div className={`w-full ${className}`}>
      {/* Progress Bar */}
      <div className="relative w-full h-2 bg-gray-200 rounded-full overflow-hidden">
        {/* First segment - Blue (50%) */}
        <div
          className="absolute left-0 top-0 h-full bg-[#3c82f6] transition-all duration-300"
          style={{
            width: `${blueSegmentWidth}%`
          }}
        />

        {/* Second segment - Purple (20%) */}
        <div
          className="absolute top-0 h-full bg-[#a855f7] transition-all duration-300"
          style={{
            left: `${blueSegmentWidth}%`,
            width: `${graySegmentWidth}%`
          }}
        />

        {/* Third segment - Golden (30%) */}
        <div
          className="absolute top-0 h-full bg-[#eaa23b] transition-all duration-300"
          style={{
            left: `${blueSegmentWidth + graySegmentWidth}%`,
            width: `${purpleSegmentWidth}%`
          }}
        />
      </div>

      {/* Progress Text */}
      <div className="flex justify-end mt-2">
        <p className="text-xs text-[#666666]">
          {purpleSegmentWidth ? purpleSegmentWidth.toFixed(0) : 0}% ({goldFiles} files) AI Ready Data In-Use
        </p>
      </div>
    </div>
  )
}
