"use client";

import { Search, Loader2, ArrowDownLeft } from "lucide-react";
import { Badge } from "@/components/ui/badge/badge";
import { Button } from "@/components/ui/button/button";
import Input from "@/components/ui/search-input/search-input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { dataService } from "@/service/dataService";

type DataAssetItemProps = {
  title: string;
  description: string;
  format?: string;
  formats?: string[];
  isImported?: boolean;
  datasetId?: string;
  onImport?: (datasetId: string) => void;
  loading?: boolean;
  error?: boolean;
};

function DataAssetItem({
  title,
  description,
  formats,
  isImported = false,
  datasetId,
  onImport,
  loading = false,
  error = false,
}: DataAssetItemProps) {
  const router = useRouter();

  const handleImportClick = () => {
    if (datasetId && onImport) {
      onImport(datasetId);
    }
  };

  const handleViewClick = () => {
    router.push('/data/world-data');
  };

  return (
    <div className="border border-[#e4e4e4] rounded-lg p-4">
      <div className="flex justify-between items-start mb-2">
        <h4 className="font-medium text-[#333333] flex-1 pr-4">{title}</h4>
        {isImported ? (
          <Button
            size="sm"
            className="bg-[#e4e4e4] text-[#666666] cursor-not-allowed"
            disabled
          >
            Imported
          </Button>
        ) : (
          <Button
            size="sm"
            className="bg-[#00b2a1] hover:bg-[#018e42] text-white"
            onClick={handleImportClick}
            disabled={loading}
          >
            <ArrowDownLeft className="w-4 h-4 mr-1" />
            {loading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              "Import"
            )}
          </Button>
        )}
      </div>

      <p className="text-sm text-[#666666] mb-3">{description}</p>

      {error && (
        <p className="text-sm text-red-500 mb-2">Failed to import dataset</p>
      )}

      {isImported && (
        <div className="flex items-center text-sm mb-3">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-4 h-4 mr-2 text-[#00b2a1]"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9 12.75l2.25 2.25L15 9.75M21 12c0 4.971-4.029 9-9 9s-9-4.029-9-9 4.029-9 9-9 9 4.029 9 9z"
            />
          </svg>
          <span className="text-black">Dataset imported.</span>
          <button
            className="text-[#00b2a1] ml-2 cursor-pointer hover:underline"
            onClick={handleViewClick}
          >
            Click here to view.
          </button>
        </div>
      )}

      <div className="flex gap-2">
        {formats &&
          formats.map((fmt) => (
            <Badge
              key={fmt}
              variant="secondary"
              className="bg-[#f0f2f8] text-[#666666]"
            >
              {fmt}
            </Badge>
          ))}
      </div>
    </div>
  );
}

export default function ExploreWorldSection() {
  const [searchQuery, setSearchQuery] = useState("");
  const [dataAssets, setDataAssets] = useState<DataAssetItemProps[]>([]);
  const [page, setPage] = useState(1);
  const [importingDatasets, setImportingDatasets] = useState<Set<string>>(new Set());
  const [importErrors, setImportErrors] = useState<Set<string>>(new Set());
  const [importedDatasets, setImportedDatasets] = useState<Set<string>>(new Set());
  const router = useRouter();

  const handleImport = async (datasetId: string) => {
    try {
      setImportingDatasets(prev => new Set(prev).add(datasetId));
      setImportErrors(prev => {
        const newSet = new Set(prev);
        newSet.delete(datasetId);
        return newSet;
      });

      const res = await dataService.importWorldData({
        datasetId: datasetId,
        orgId: "",
        token: "",
      });

      if (res.status === 200) {
        setImportedDatasets(prev => new Set(prev).add(datasetId));
      } else {
        setImportErrors(prev => new Set(prev).add(datasetId));
      }
    } catch (error) {
      setImportErrors(prev => new Set(prev).add(datasetId));
    } finally {
      setImportingDatasets(prev => {
        const newSet = new Set(prev);
        newSet.delete(datasetId);
        return newSet;
      });
    }
  };

  useEffect(() => {
    const fetchDataAssets = async () => {
      try {
        const res = await dataService.getExternalSearchData(page);
        const data = await res.data.data;
        console.log("Fetched Data Assets ID:", data[0].did);
        const filteredData = data.filter((item: any) => !item.isImported);
        // Now, if filteredData is empty then we recall the API with next page
        if (filteredData.length === 0) {
          const hasNextPage = res.data.count > page * 10;
          if (hasNextPage) {
            setPage((prev) => prev + 1);
          }
          return;
        }
        // Map the data to the required format
        const dataParsed = filteredData.map((item: any) => ({
          title: item.title,
          description: item.description.substring(0, 250) + "...",
          format: item.filetypes[0] || "Unknown",
          formats: item.filetypes.length > 1 ? item.filetypes : undefined,
          datasetId: item.did,
          isImported: item.isImported,
        }));
        setDataAssets(dataParsed);
      } catch (error) {
        console.error("Error fetching data assets:", error);
      }
    };
    fetchDataAssets();
  }, [page]);

  const handleOnSearchClick = () => {
    const url = `/data/world-data?q=${searchQuery}`;
    router.push(url);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="w-5 h-5" />
          Explore World Data
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#888888] w-4 h-4" />
          <Input
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
            }}
            onSearchClick={handleOnSearchClick}
            placeholder="Search for Data Assets"
            className="pl-10 bg-white border-[#cfd2de] w-full"
          />
        </div>

        <div className="space-y-4">
          <h4 className="font-medium text-[#333333]">Suggested Data Assets</h4>

          {dataAssets.length > 0 ? (
            dataAssets.map((asset, index) => {
              const datasetId = asset.datasetId || "";
              const isImported = asset.isImported || importedDatasets.has(datasetId);
              const isLoading = importingDatasets.has(datasetId);
              const hasError = importErrors.has(datasetId);

              return (
                <DataAssetItem
                  key={asset.datasetId || index}
                  title={asset.title}
                  description={asset.description}
                  formats={asset.formats}
                  isImported={isImported}
                  datasetId={datasetId}
                  onImport={handleImport}
                  loading={isLoading}
                  error={hasError}
                />
              );
            })
          ) : (
            <p className="text-[#888888]">No data assets found.</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
