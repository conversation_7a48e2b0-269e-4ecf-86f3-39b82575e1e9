"use client"

import { Database, TriangleAlert } from "lucide-react"
import { Badge } from "@/components/ui/badge/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useEffect, useState } from "react"
import { DatasageService } from "@/service/datasage"
import { circIn } from "framer-motion"

export type ConnectionItemProps = {
  name: string
  type: string
  category: string
  status: string
  statusColor: string
  critical?: boolean
  failureReason?: string
  lastSync?: string
}

function ConnectionItem({
  name,
  type,
  category,
  status,
  statusColor,
  critical = false,
  failureReason,
  lastSync,
}: ConnectionItemProps) {
  if (critical) {
    return (
      <div className="border border-[#e4e4e4] bg-white rounded-lg p-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-bold text-[#333333]">{name}</span>
            <Badge variant={"outline"} className="bg-red-100 text-[#dc2625] rounded-[16px]"><TriangleAlert className="w-4 h-4" /> Critical</Badge>
          </div>
          <Badge variant="destructive" className={`relative top-5 right-2 text-sm bg-[#DCFCE7] !rounded-[16px] ${statusColor}`}>• Inactive</Badge>
        </div>
        <p className="text-sm text-[#666666] mb-2">Last Sync: {lastSync}</p>
        <p className="text-sm text-[#666666]">{failureReason}</p>
      </div>
    );
  } else {
    return (
      <div className="border border-[#e4e4e4] rounded-lg p-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-bold text-[#333333]">{name}</span>
          </div>
          <Badge variant="destructive" className={`relative top-8 right-2 text-sm bg-[#DCFCE7] !rounded-[16px] ${statusColor}`}>• {status == "Online" ? "Active" : "Inactive"}</Badge>
        </div>
        <p className="text-sm text-[#666666] mb-2">DBMS: {type}</p>
        <p className="text-sm text-[#666666]">Category: {category}</p>
      </div>
    );
  }
}

export default function ConnectionsSection({ connections }: { connections: ConnectionItemProps[] }) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Connections
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {connections.length > 0 ? (
          connections.map((conn, index) => (
            <ConnectionItem
              key={index}
              name={conn.name}
              type={conn.type}
              category={conn.category}
              status={conn.status}
              statusColor={conn.statusColor}
              critical={conn.critical}
              failureReason={conn.failureReason}
              lastSync={conn.lastSync}
            />
          ))
        ) : (
          <p className="text-sm text-[#666666]">No connections available.</p>
        )}
      </CardContent>
    </Card>
  )
}
