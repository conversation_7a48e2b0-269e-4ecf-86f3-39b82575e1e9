"use client"

import { Info, Plus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button/button"
import ActionSection from "./components/ActionSection"
import DataAssetsSection from "./components/DataAssetsSection"
import ConnectionsSection, { ConnectionItemProps } from "./components/ConnectionsSection"
import ExploreWorldSection from "./components/ExploreWorldSection"
import { useEffect, useState } from "react"
import UploadModal from "./components/UploadModal"
import { Toast } from "@/components/ui/toast/toast"
import { DatasageService } from "@/service/datasage"

export default function DataPage() {
  const [openUploadModal, setOpenUploadModal] = useState(false)
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" } | null>(null);
  const [connections, setConnections] = useState<ConnectionItemProps[]>([]);

  useEffect(() => {
    const fetchConnections = async () => {
      try {
        const res = await DatasageService.getDatasageConnections();
        const data = res.data.data;
        const fetchedConnections = data.map((i: any) => ({
          name: i.source_name,
          type: i.description,
          category: i.source_type,
          status: i.connection_status,
          statusColor: i.connection_status === "Online" ? "text-[#018e42]" : "text-[#dc2625]",
          critical: i.critical || false,
          failureReason: i.downtime_reason,
          lastSync: i.last_checked || "N/A",
        }));
        // console.log("Fetched Connections:", data);
        setConnections(fetchedConnections);
      } catch (error) {
        console.error("Error fetching connections:", error);
      }
    };
    fetchConnections();
  }, []);
  return (
    <div className="w-full h-full bg-[#fafcff]">
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
          dismissTime={5000}
        />
      )}
      {/* Upload Modal */}
      <UploadModal setOpen={setOpenUploadModal} open={openUploadModal} onUpload={() => setToast({ message: "Files uploaded successfully!", type: "success" })}/>
      {/* Main Content */}
      <div className="w-full h-full flex flex-col">
        {/* Header */}
        <div className="bg-white p-6 pb-0">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-semibold text-[#333333]">Data Overview</h1>
              <p className="text-[#666666] text-sm mt-1">
                Easily track your data connection errors, data status, resolve issues, view requirements, and upload
                necessary documents.
              </p>
            </div>
            <Button variant="outline" onClick={() => setOpenUploadModal(true)} className="text-[#00b2a1] hover:bg-[#00b2a1] hover:text-white">
              <Plus className="w-4 h-4 mr-2" />
              Upload Data
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {/* Info Banner */}
          <div className="rounded-lg pb-3 mb-1 flex justify-start items-center gap-3">
            <Info className="w-5 h-5" />
            <p className="text-[#666666] text-sm">
              It may take some time for data to complete the Sutra AI data unification process and be viewable in the
              dashboard. If you don't see assets you have uploaded, please check back later.
            </p>
          </div>

          <ActionSection inactiveDbms={connections.filter(conn => conn.status !== "Online")}/>
          <DataAssetsSection />
          <ConnectionsSection connections={connections}/>
          <ExploreWorldSection />
        </div>
      </div>
    </div>
  )
}
