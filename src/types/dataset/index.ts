import { Project } from "@/service/types/types";

export type ExploreDataResponse = {
  worldData: {
    count: number;
  };
  companyDataset: DatasetResponse;
  projectData: {
    projects: Project[];
    totalCount: number;
  };
};

export type DatasetResponse = {
  datasets: Dataset[];
  pagination: Pagination;
  filters: Filters;
};

export type Dataset = {
  title: string;
  description: string;
  visibility: "private" | "public";
  department: string[];
  projectIds: string[];
  isWorldDataset: boolean;
  files: FileDetail[];
  fileTypes: string[];
  tags: string[];
  license: string;
};

export interface ProjectData {
  _id: string;
  name: string;
  description: string;
  appType?: string;
  performance?: string;
}

export type FileDetail = {
  filename: string;
  filesize: number;
  url: string;
  description: string;
};

export type Pagination = {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type Filters = {
  tags: string[];
  licenses: string[];
  fileTypes: string[];
};

export interface MatchedEntityAttribute {
  database: string | null;
  type: string;
  attribute: string;
  entity_attribute_description: string;
  reason: string;
  confidence_score: number;
  matched_dataset_attribute: string | null;
  dataset: string | null;
}

export interface ActionItemData {
  _id: string;
  name: string;
  entity: string;
  entity_description: string;
  matched_entity_attributes: MatchedEntityAttribute[];
  dataCompletionPercentage: number
}

export interface ActionItemsResponse {
  total: number;
  page: number;
  limit: number;
  data: ActionItemData[];
}
